from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field

class SearchCriteria(BaseModel):
    space_key: Optional[str] = Field(None, description="Clé de l'espace Confluence")
    labels: Optional[List[str]] = Field(default=[], description="Liste des labels à filtrer")
    start_date: Optional[datetime] = Field(None, description="Date de début pour la recherche")
    end_date: Optional[datetime] = Field(None, description="Date de fin pour la recherche")
    page_limit: Optional[int] = Field(default=100, description="Nombre maximum de pages à récupérer")

class ConfluenceConfig(BaseModel):
    url: str = Field(..., description="URL de l'instance Confluence")
    username: str = Field(..., description="Nom d'utilisateur")
    api_token: str = Field(..., description="Token d'API")

class StorageConfig(BaseModel):
    path: str = Field(..., description="Chemin de stockage")
    max_size: int = Field(default=1000000, description="Taille maximale en octets")

class ProcessingConfig(BaseModel):
    batch_size: int = Field(default=10, description="Taille des lots de traitement")
    max_threads: int = Field(default=4, description="Nombre maximum de threads")
    timeout: int = Field(default=300, description="Délai d'expiration en secondes")
    retry_count: int = Field(default=3, description="Nombre de tentatives de traitement")
    max_file_size: int = Field(default=10_000_000, description="Taille maximale des fichiers en octets")

class ThreadPoolConfig(BaseModel):
    min_workers: int = Field(default=2, description="Nombre minimum de workers dans le pool")
    max_workers: int = Field(default=10, description="Nombre maximum de workers dans le pool")
    thread_name_prefix: str = Field(default="confluence-sync", description="Préfixe pour les noms des threads")
    queue_size: int = Field(default=100, description="Taille maximale de la file d'attente")
    keep_alive_time: int = Field(default=60, description="Temps de maintien des threads inactifs (secondes)")

class RetryConfig(BaseModel):
    max_attempts: int = Field(default=3, description="Nombre maximum de tentatives")
    initial_delay: float = Field(default=1.0, description="Délai initial entre les tentatives (secondes)")
    max_delay: float = Field(default=60.0, description="Délai maximum entre les tentatives (secondes)")
    exponential_base: float = Field(default=2.0, description="Base pour le délai exponentiel")
    jitter: bool = Field(default=True, description="Ajouter un délai aléatoire")

class CircuitBreakerConfig(BaseModel):
    failure_threshold: int = Field(default=5, description="Nombre d'échecs avant ouverture")
    reset_timeout: float = Field(default=60.0, description="Délai avant tentative de réinitialisation (secondes)")
    half_open_timeout: float = Field(default=30.0, description="Délai en état semi-ouvert (secondes)")
    exception_types: List[str] = Field(
        default=["ConnectionError", "TimeoutError"],
        description="Types d'exceptions à surveiller"
    )
    minimum_throughput: int = Field(default=10, description="Nombre minimum de requêtes avant activation")
